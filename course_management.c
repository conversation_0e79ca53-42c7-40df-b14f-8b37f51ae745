#include "course_system.h"

// Local static variable for course ID generation
static int next_course_id = 1;

void add_course(void) {
    if (total_courses >= MAX_COURSES) {
        printf("Maximum course limit reached!\n");
        return;
    }
    
    Course *new_course = &courses[total_courses];
    
    printf("\n=== Add New Course ===\n");
    printf("Enter course code: ");
    get_string_input(new_course->course_code, MAX_CODE_LENGTH);
    
    // Check if course code already exists using for loop
    for (int i = 0; i < total_courses; i++) {
        if (strcmp(courses[i].course_code, new_course->course_code) == 0) {
            printf("Course code already exists!\n");
            return;
        }
    }
    
    printf("Enter course name: ");
    get_string_input(new_course->course_name, MAX_NAME_LENGTH);
    
    printf("Enter credits (1-4): ");
    new_course->credits = get_integer_input();
    
    // Input validation using if-else
    if (new_course->credits < 1 || new_course->credits > 4) {
        printf("Invalid credits! Setting to 3.\n");
        new_course->credits = 3;
    }
    
    printf("Enter prerequisites (or 'None'): ");
    get_string_input(new_course->prerequisites, MAX_NAME_LENGTH);
    
    printf("Enter instructor name: ");
    get_string_input(new_course->instructor, MAX_NAME_LENGTH);
    
    total_courses++;
    printf("Course added successfully! Total courses: %d\n", total_courses);
}

void display_courses(void) {
    if (total_courses == 0) {
        printf("No courses available.\n");
        return;
    }
    
    printf("\n=== Course List ===\n");
    printf("%-10s %-30s %-8s %-20s %-20s\n", 
           "Code", "Name", "Credits", "Prerequisites", "Instructor");
    printf("--------------------------------------------------------------------------------\n");
    
    // Using for loop to display courses
    for (int i = 0; i < total_courses; i++) {
        print_course_details(&courses[i]);
    }
}

void search_course_by_code(char *code) {
    int found = 0;
    
    // Linear search using while loop
    int i = 0;
    while (i < total_courses && !found) {
        if (strcmp(courses[i].course_code, code) == 0) {
            found = 1;
            printf("\n=== Course Found ===\n");
            printf("%-10s %-30s %-8s %-20s %-20s\n", 
                   "Code", "Name", "Credits", "Prerequisites", "Instructor");
            printf("--------------------------------------------------------------------------------\n");
            print_course_details(&courses[i]);
        }
        i++;
    }
    
    if (!found) {
        printf("Course with code '%s' not found.\n", code);
    }
}

void update_course(void) {
    if (total_courses == 0) {
        printf("No courses available to update.\n");
        return;
    }

    printf("\n=== Update Course ===\n");
    display_courses();

    printf("\nEnter course code to update: ");
    char course_code[MAX_CODE_LENGTH];
    get_string_input(course_code, MAX_CODE_LENGTH);

    // Find course using linear search
    int course_index = -1;
    for (int i = 0; i < total_courses; i++) {
        if (strcmp(courses[i].course_code, course_code) == 0) {
            course_index = i;
            break;
        }
    }

    if (course_index == -1) {
        printf("Course with code '%s' not found.\n", course_code);
        return;
    }

    Course *course = &courses[course_index];
    printf("\nCurrent course details:\n");
    printf("%-10s %-30s %-8s %-20s %-20s\n",
           "Code", "Name", "Credits", "Prerequisites", "Instructor");
    printf("--------------------------------------------------------------------------------\n");
    print_course_details(course);

    printf("\n=== Update Course Information ===\n");
    printf("1. Update Course Name\n");
    printf("2. Update Credits\n");
    printf("3. Update Prerequisites\n");
    printf("4. Update Instructor\n");
    printf("5. Update All Information\n");
    printf("Enter choice (1-5): ");

    int choice = get_integer_input();

    switch(choice) {
        case 1:
            printf("Enter new course name: ");
            get_string_input(course->course_name, MAX_NAME_LENGTH);
            break;
        case 2:
            printf("Enter new credits (1-4): ");
            course->credits = get_integer_input();
            if (course->credits < 1 || course->credits > 4) {
                printf("Invalid credits! Setting to 3.\n");
                course->credits = 3;
            }
            break;
        case 3:
            printf("Enter new prerequisites: ");
            get_string_input(course->prerequisites, MAX_NAME_LENGTH);
            break;
        case 4:
            printf("Enter new instructor name: ");
            get_string_input(course->instructor, MAX_NAME_LENGTH);
            break;
        case 5:
            printf("Enter new course name: ");
            get_string_input(course->course_name, MAX_NAME_LENGTH);
            printf("Enter new credits (1-4): ");
            course->credits = get_integer_input();
            if (course->credits < 1 || course->credits > 4) {
                printf("Invalid credits! Setting to 3.\n");
                course->credits = 3;
            }
            printf("Enter new prerequisites: ");
            get_string_input(course->prerequisites, MAX_NAME_LENGTH);
            printf("Enter new instructor name: ");
            get_string_input(course->instructor, MAX_NAME_LENGTH);
            break;
        default:
            printf("Invalid choice!\n");
            return;
    }

    printf("Course updated successfully!\n");
    printf("\nUpdated course details:\n");
    printf("%-10s %-30s %-8s %-20s %-20s\n",
           "Code", "Name", "Credits", "Prerequisites", "Instructor");
    printf("--------------------------------------------------------------------------------\n");
    print_course_details(course);
}

void delete_course(void) {
    if (total_courses == 0) {
        printf("No courses available to delete.\n");
        return;
    }

    printf("\n=== Delete Course ===\n");
    display_courses();

    printf("\nEnter course code to delete: ");
    char course_code[MAX_CODE_LENGTH];
    get_string_input(course_code, MAX_CODE_LENGTH);

    // Find course using linear search
    int course_index = -1;
    for (int i = 0; i < total_courses; i++) {
        if (strcmp(courses[i].course_code, course_code) == 0) {
            course_index = i;
            break;
        }
    }

    if (course_index == -1) {
        printf("Course with code '%s' not found.\n", course_code);
        return;
    }

    // Check if course has sections
    int has_sections = 0;
    for (int i = 0; i < total_sections; i++) {
        if (strcmp(sections[i].course_code, course_code) == 0) {
            has_sections = 1;
            break;
        }
    }

    if (has_sections) {
        printf("Cannot delete course '%s' because it has sections.\n", course_code);
        printf("Please delete all sections for this course first.\n");
        return;
    }

    printf("\nCourse to be deleted:\n");
    printf("%-10s %-30s %-8s %-20s %-20s\n",
           "Code", "Name", "Credits", "Prerequisites", "Instructor");
    printf("--------------------------------------------------------------------------------\n");
    print_course_details(&courses[course_index]);

    printf("\nAre you sure you want to delete this course? (y/n): ");
    char confirm[10];
    get_string_input(confirm, 10);

    if (confirm[0] != 'y' && confirm[0] != 'Y') {
        printf("Course deletion cancelled.\n");
        return;
    }

    // Shift remaining courses to fill the gap
    for (int i = course_index; i < total_courses - 1; i++) {
        courses[i] = courses[i + 1];
    }

    total_courses--;
    printf("Course '%s' deleted successfully! Total courses: %d\n", course_code, total_courses);
}

// Bubble Sort implementation for sorting courses by name
void sort_courses_by_name_bubble(void) {
    if (total_courses <= 1) return;
    
    // Bubble sort using nested for loops
    for (int i = 0; i < total_courses - 1; i++) {
        for (int j = 0; j < total_courses - i - 1; j++) {
            // Compare course names using strcmp
            if (strcmp(courses[j].course_name, courses[j + 1].course_name) > 0) {
                swap_courses(&courses[j], &courses[j + 1]);
            }
        }
    }
}

// Selection Sort implementation for sorting courses by credits
void sort_courses_by_credits_selection(void) {
    if (total_courses <= 1) return;
    
    // Selection sort using nested loops
    for (int i = 0; i < total_courses - 1; i++) {
        int min_index = i;
        
        // Find minimum element using for loop
        for (int j = i + 1; j < total_courses; j++) {
            if (courses[j].credits < courses[min_index].credits) {
                min_index = j;
            }
        }
        
        // Swap if minimum is not at current position
        if (min_index != i) {
            swap_courses(&courses[i], &courses[min_index]);
        }
    }
}

void add_section(void) {
    if (total_sections >= MAX_SECTIONS) {
        printf("Maximum section limit reached!\n");
        return;
    }
    
    if (total_courses == 0) {
        printf("No courses available. Please add courses first.\n");
        return;
    }
    
    Section *new_section = &sections[total_sections];
    
    printf("\n=== Add New Section ===\n");
    printf("Available courses:\n");
    display_courses();
    
    printf("\nEnter course code for this section: ");
    get_string_input(new_section->course_code, MAX_CODE_LENGTH);
    
    // Validate course code exists
    int course_exists = 0;
    for (int i = 0; i < total_courses; i++) {
        if (strcmp(courses[i].course_code, new_section->course_code) == 0) {
            course_exists = 1;
            break;
        }
    }
    
    if (!course_exists) {
        printf("Invalid course code!\n");
        return;
    }
    
    printf("Enter section ID: ");
    get_string_input(new_section->section_id, MAX_CODE_LENGTH);
    
    printf("Enter time slot (e.g., 'MWF 10:00-11:00'): ");
    get_string_input(new_section->time_slot, MAX_NAME_LENGTH);
    
    printf("Enter room number: ");
    get_string_input(new_section->room, MAX_NAME_LENGTH);
    
    printf("Enter capacity (1-30): ");
    new_section->capacity = get_integer_input();
    
    if (new_section->capacity < 1 || new_section->capacity > MAX_ENROLLED) {
        printf("Invalid capacity! Setting to 25.\n");
        new_section->capacity = 25;
    }
    
    new_section->enrolled_count = 0;
    
    // Initialize student IDs array using for loop
    for (int i = 0; i < MAX_ENROLLED; i++) {
        new_section->student_ids[i] = -1; // -1 indicates empty slot
    }
    
    total_sections++;
    printf("Section added successfully! Total sections: %d\n", total_sections);
}

void display_sections(void) {
    if (total_sections == 0) {
        printf("No sections available.\n");
        return;
    }
    
    printf("\n=== Section List ===\n");
    printf("%-10s %-10s %-20s %-15s %-8s %-8s\n", 
           "Section", "Course", "Time Slot", "Room", "Capacity", "Enrolled");
    printf("--------------------------------------------------------------------------------\n");
    
    for (int i = 0; i < total_sections; i++) {
        print_section_details(&sections[i]);
    }
}

void display_sections_for_course(char *course_code) {
    int found = 0;
    
    printf("\n=== Sections for Course: %s ===\n", course_code);
    printf("%-10s %-20s %-15s %-8s %-8s\n", 
           "Section", "Time Slot", "Room", "Capacity", "Enrolled");
    printf("----------------------------------------------------------------\n");
    
    for (int i = 0; i < total_sections; i++) {
        if (strcmp(sections[i].course_code, course_code) == 0) {
            printf("%-10s %-20s %-15s %-8d %-8d\n",
                   sections[i].section_id,
                   sections[i].time_slot,
                   sections[i].room,
                   sections[i].capacity,
                   sections[i].enrolled_count);
            found = 1;
        }
    }
    
    if (!found) {
        printf("No sections found for course '%s'.\n", course_code);
    }
}

Section* find_section_by_id(char *section_id) {
    for (int i = 0; i < total_sections; i++) {
        if (strcmp(sections[i].section_id, section_id) == 0) {
            return &sections[i];
        }
    }
    return NULL;
}

void update_section(void) {
    if (total_sections == 0) {
        printf("No sections available to update.\n");
        return;
    }

    printf("\n=== Update Section ===\n");
    display_sections();

    printf("\nEnter section ID to update: ");
    char section_id[MAX_CODE_LENGTH];
    get_string_input(section_id, MAX_CODE_LENGTH);

    // Find section using linear search
    int section_index = -1;
    for (int i = 0; i < total_sections; i++) {
        if (strcmp(sections[i].section_id, section_id) == 0) {
            section_index = i;
            break;
        }
    }

    if (section_index == -1) {
        printf("Section with ID '%s' not found.\n", section_id);
        return;
    }

    Section *section = &sections[section_index];
    printf("\nCurrent section details:\n");
    printf("%-10s %-10s %-20s %-15s %-8s %-8s\n",
           "Section", "Course", "Time Slot", "Room", "Capacity", "Enrolled");
    printf("--------------------------------------------------------------------------------\n");
    print_section_details(section);

    printf("\n=== Update Section Information ===\n");
    printf("1. Update Time Slot\n");
    printf("2. Update Room\n");
    printf("3. Update Capacity\n");
    printf("4. Update All Information\n");
    printf("Enter choice (1-4): ");

    int choice = get_integer_input();

    switch(choice) {
        case 1:
            printf("Enter new time slot: ");
            get_string_input(section->time_slot, MAX_NAME_LENGTH);
            break;
        case 2:
            printf("Enter new room: ");
            get_string_input(section->room, MAX_NAME_LENGTH);
            break;
        case 3:
            printf("Enter new capacity (1-30): ");
            int new_capacity = get_integer_input();
            if (new_capacity < section->enrolled_count) {
                printf("Cannot set capacity lower than current enrollment (%d)!\n", section->enrolled_count);
                return;
            }
            if (new_capacity < 1 || new_capacity > MAX_ENROLLED) {
                printf("Invalid capacity! Must be between 1 and %d.\n", MAX_ENROLLED);
                return;
            }
            section->capacity = new_capacity;
            break;
        case 4:
            printf("Enter new time slot: ");
            get_string_input(section->time_slot, MAX_NAME_LENGTH);
            printf("Enter new room: ");
            get_string_input(section->room, MAX_NAME_LENGTH);
            printf("Enter new capacity (1-30): ");
            new_capacity = get_integer_input();
            if (new_capacity < section->enrolled_count) {
                printf("Cannot set capacity lower than current enrollment (%d)!\n", section->enrolled_count);
                return;
            }
            if (new_capacity < 1 || new_capacity > MAX_ENROLLED) {
                printf("Invalid capacity! Must be between 1 and %d.\n", MAX_ENROLLED);
                return;
            }
            section->capacity = new_capacity;
            break;
        default:
            printf("Invalid choice!\n");
            return;
    }

    printf("Section updated successfully!\n");
    printf("\nUpdated section details:\n");
    printf("%-10s %-10s %-20s %-15s %-8s %-8s\n",
           "Section", "Course", "Time Slot", "Room", "Capacity", "Enrolled");
    printf("--------------------------------------------------------------------------------\n");
    print_section_details(section);
}

void delete_section(void) {
    if (total_sections == 0) {
        printf("No sections available to delete.\n");
        return;
    }

    printf("\n=== Delete Section ===\n");
    display_sections();

    printf("\nEnter section ID to delete: ");
    char section_id[MAX_CODE_LENGTH];
    get_string_input(section_id, MAX_CODE_LENGTH);

    // Find section using linear search
    int section_index = -1;
    for (int i = 0; i < total_sections; i++) {
        if (strcmp(sections[i].section_id, section_id) == 0) {
            section_index = i;
            break;
        }
    }

    if (section_index == -1) {
        printf("Section with ID '%s' not found.\n", section_id);
        return;
    }

    Section *section = &sections[section_index];

    if (section->enrolled_count > 0) {
        printf("Cannot delete section '%s' because it has %d enrolled students.\n",
               section_id, section->enrolled_count);
        printf("Please remove all students from this section first.\n");
        return;
    }

    printf("\nSection to be deleted:\n");
    printf("%-10s %-10s %-20s %-15s %-8s %-8s\n",
           "Section", "Course", "Time Slot", "Room", "Capacity", "Enrolled");
    printf("--------------------------------------------------------------------------------\n");
    print_section_details(section);

    printf("\nAre you sure you want to delete this section? (y/n): ");
    char confirm[10];
    get_string_input(confirm, 10);

    if (confirm[0] != 'y' && confirm[0] != 'Y') {
        printf("Section deletion cancelled.\n");
        return;
    }

    // Shift remaining sections to fill the gap
    for (int i = section_index; i < total_sections - 1; i++) {
        sections[i] = sections[i + 1];
    }

    total_sections--;
    printf("Section '%s' deleted successfully! Total sections: %d\n", section_id, total_sections);
}

// Advanced function using pointer to pointer
void sort_courses_using_pointers(Course **course_ptrs, int count) {
    // Bubble sort using pointer to pointer
    for (int i = 0; i < count - 1; i++) {
        for (int j = 0; j < count - i - 1; j++) {
            if (strcmp(course_ptrs[j]->course_name, course_ptrs[j + 1]->course_name) > 0) {
                Course *temp = course_ptrs[j];
                course_ptrs[j] = course_ptrs[j + 1];
                course_ptrs[j + 1] = temp;
            }
        }
    }
}

void display_courses_using_pointer_to_pointer(Course **course_ptrs, int count) {
    printf("%-10s %-30s %-8s %-20s %-20s\n", 
           "Code", "Name", "Credits", "Prerequisites", "Instructor");
    printf("--------------------------------------------------------------------------------\n");
    
    // Using pointer arithmetic to access courses
    for (int i = 0; i < count; i++) {
        Course *current_course = *(course_ptrs + i); // Pointer arithmetic
        print_course_details(current_course);
    }
}
