# University Pre-Registration & Course Selection System

A comprehensive C programming project that demonstrates fundamental C concepts through a practical university course registration system.

## 📚 C Programming Concepts Covered

### 1. **Computer and C Fundamentals**
- Complete C program structure with multiple source files
- Header files and modular programming
- Compilation using Makefile

### 2. **C Data Types**
- `int` - Student IDs, course credits, capacities
- `char` - Strings for names, codes, descriptions
- `float` - GPA calculations
- Arrays of different data types
- Custom data types using `typedef`

### 3. **Operators and Expressions**
- **Arithmetic operators**: `+`, `-`, `*`, `/`, `%`
- **Relational operators**: `==`, `!=`, `<`, `>`, `<=`, `>=`
- **Logical operators**: `&&`, `||`, `!`
- **Assignment operators**: `=`, `+=`, `-=`
- **Increment/Decrement**: `++`, `--`

### 4. **Basic I/O Functions**
- `printf()` - Formatted output for menus and data display
- `scanf()` - User input for numbers and strings
- `fgets()` - Safe string input
- `fprintf()` and `fscanf()` - File I/O operations
- Input validation and error handling

### 5. **Control Structures**

#### **If-else Statements**
- Input validation (GPA range, semester validation)
- Prerequisite checking
- Time conflict detection
- Menu choice validation

#### **Loops**

##### **For Loop**
- Iterating through course arrays
- Displaying student lists
- Sorting algorithms implementation
- Array initialization

##### **While Loop**
- Linear search operations
- Menu input validation
- File reading operations

##### **Do-while Loop**
- Main program menu loop
- Input validation loops
- User interaction loops

### 6. **Functions**
- **Function declarations** in header files
- **Function definitions** across multiple source files
- **Parameter passing** by value and by reference (pointers)
- **Return values** for validation and search operations
- **Recursive potential** in sorting algorithms

### 7. **Variable Types**

#### **Global Variables**
- `total_courses`, `total_students`, `total_sections`
- Global arrays: `courses[]`, `students[]`, `sections[]`

#### **Local Variables**
- Function parameters and local variables
- Loop counters and temporary variables

#### **Static Variables**
- `next_student_id` - Maintains student ID sequence
- `menu_access_count` - Tracks menu usage
- `input_validation_errors` - Error counting

#### **Register Variables**
- `register int choice` - Frequently used menu choice variable

### 8. **Arrays**
- **1D Arrays**: Course codes, student names
- **2D Arrays**: Student registered courses `char registered_courses[10][MAX_CODE_LENGTH]`
- **Array of Structures**: `Course courses[MAX_COURSES]`
- **Array indexing** and **bounds checking**

### 9. **Sorting Algorithms**

#### **Bubble Sort**
- `sort_courses_by_name_bubble()` - Sorts courses alphabetically
- Nested loops with comparison and swapping
- Time complexity demonstration

#### **Selection Sort**
- `sort_courses_by_credits_selection()` - Sorts by credit hours
- Finding minimum element and swapping
- Algorithm comparison with bubble sort

### 10. **Pointer Concepts**

#### **Pointer Arithmetic**
- `Student *student_ptr = students;`
- `(student_ptr + i)->student_id` - Accessing array elements
- `*(course_ptrs + i)` - Dereferencing with arithmetic

#### **Arrays and Pointers**
- Array names as pointers
- Pointer-based array traversal
- Function parameters as pointers

#### **Pointer to Pointer**
- `Course **course_ptrs` - Dynamic array of pointers
- `display_courses_using_pointer_to_pointer()` function
- Advanced pointer manipulation

### 11. **Structures**
```c
typedef struct {
    char course_code[MAX_CODE_LENGTH];
    char course_name[MAX_NAME_LENGTH];
    int credits;
    char prerequisites[MAX_NAME_LENGTH];
    char instructor[MAX_NAME_LENGTH];
} Course;
```

- **Structure definition** with `typedef`
- **Structure members** access using `.` and `->`
- **Array of structures** for data management
- **Nested structures** concepts
- **Structure assignment** and copying

## 🚀 Features

### Course Management
- Add new courses with validation
- Display course catalog
- Search courses by code
- Sort courses (Bubble Sort & Selection Sort)

### Section Management
- Create course sections with time slots
- Manage section capacity and enrollment
- Display sections by course

### Student Management
- Student registration with auto-generated IDs
- GPA and semester tracking
- Course enrollment with prerequisite checking
- Time conflict detection
- Drop/add functionality

### Advanced Features
- **File I/O**: Save/load data persistence
- **Pointer demonstrations**: Advanced pointer operations
- **Memory management**: Dynamic allocation examples
- **Input validation**: Robust error handling

## 🛠️ Compilation and Usage

### Using Makefile
```bash
# Compile the program
make

# Run the program
make run

# Create sample data for testing
make sample_data

# Clean build files
make clean

# Build with debug information
make debug
```

### Manual Compilation
```bash
gcc -Wall -Wextra -std=c99 -g main.c course_management.c student_management.c utils.c -o course_system
```

## 📁 File Structure
```
pre-registration-app/
├── main.c                 # Main program and menu system
├── course_system.h        # Header file with all declarations
├── course_management.c    # Course and section management
├── student_management.c   # Student operations
├── utils.c               # Utility functions and file I/O
├── Makefile              # Build configuration
├── README.md             # This documentation
└── course_data.txt       # Data persistence file (created at runtime)
```

## 🎯 Learning Objectives

This project helps students understand:

1. **Modular Programming**: Breaking large programs into manageable modules
2. **Data Structures**: Using structures and arrays effectively
3. **Algorithm Implementation**: Sorting and searching algorithms
4. **Memory Management**: Pointer usage and memory allocation
5. **File Operations**: Data persistence and file handling
6. **Input Validation**: Robust user input handling
7. **Real-world Application**: Practical software development

## 🔧 Sample Usage

1. **Start the program**: `./course_system`
2. **Add courses**: Use menu option 1
3. **Create sections**: Use menu option 4
4. **Register students**: Use menu option 6
5. **Enroll students**: Use menu option 8
6. **View data**: Various display options
7. **Save data**: Menu option 13

## 📊 Data Persistence

The system automatically saves data to `course_data.txt` and loads it on startup, demonstrating:
- File I/O operations
- Data serialization
- Error handling for file operations

## 🎓 Educational Value

This project serves as a comprehensive example for learning C programming, covering all fundamental concepts while building a practical, real-world application that students can relate to and extend.
