#ifndef COURSE_SYSTEM_H
#define COURSE_SYSTEM_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Constants and Macros
#define MAX_COURSES 50
#define MAX_STUDENTS 100
#define MAX_SECTIONS 10
#define MAX_NAME_LENGTH 50
#define MAX_CODE_LENGTH 10
#define MAX_ENROLLED 30

// Global variables
extern int total_courses;
extern int total_students;
extern int total_sections;

// Structure definitions
typedef struct {
    char course_code[MAX_CODE_LENGTH];
    char course_name[MAX_NAME_LENGTH];
    int credits;
    char prerequisites[MAX_NAME_LENGTH];
    char instructor[MAX_NAME_LENGTH];
} Course;

typedef struct {
    char section_id[MAX_CODE_LENGTH];
    char course_code[MAX_CODE_LENGTH];
    char time_slot[MAX_NAME_LENGTH];
    char room[MAX_NAME_LENGTH];
    int capacity;
    int enrolled_count;
    int student_ids[MAX_ENROLLED];
} Section;

typedef struct {
    int student_id;
    char name[MAX_NAME_LENGTH];
    char email[MAX_NAME_LENGTH];
    int semester;
    float gpa;
    int registered_courses_count;
    char registered_courses[10][MAX_CODE_LENGTH];
    int registered_sections[10];
} Student;

// Global arrays
extern Course courses[MAX_COURSES];
extern Section sections[MAX_SECTIONS];
extern Student students[MAX_STUDENTS];

// Function declarations

// Course Management Functions
void add_course(void);
void display_courses(void);
void search_course_by_code(char *code);
void update_course(void);
void delete_course(void);
void sort_courses_by_name_bubble(void);
void sort_courses_by_credits_selection(void);

// Section Management Functions
void add_section(void);
void display_sections(void);
void display_sections_for_course(char *course_code);
void update_section(void);
void delete_section(void);
Section* find_section_by_id(char *section_id);

// Student Management Functions
void add_student(void);
void display_students(void);
Student* find_student_by_id(int id);
void update_student(void);
void delete_student(void);
void search_student_by_name(void);
void register_student_for_section(void);
void drop_student_from_section(void);

// Utility Functions
void clear_input_buffer(void);
void display_menu(void);
int get_integer_input(void);
float get_float_input(void);
void get_string_input(char *str, int max_length);
void swap_courses(Course *a, Course *b);
void print_course_details(Course *course);
void print_section_details(Section *section);
void print_student_details(Student *student);

// Advanced pointer functions
void sort_courses_using_pointers(Course **course_ptrs, int count);
void display_courses_using_pointer_to_pointer(Course **course_ptrs, int count);

// File operations
void save_data_to_file(void);
void load_data_from_file(void);

// Validation functions
int validate_prerequisites(int student_id, char *course_code);
int check_time_conflict(int student_id, char *new_time_slot);
int is_section_full(int section_index);

#endif // COURSE_SYSTEM_H
