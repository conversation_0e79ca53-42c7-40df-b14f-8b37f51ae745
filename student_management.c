#include "course_system.h"

// Static variable for student ID generation
static int next_student_id = 1001;

void add_student(void) {
    if (total_students >= MAX_STUDENTS) {
        printf("Maximum student limit reached!\n");
        return;
    }
    
    Student *new_student = &students[total_students];
    
    printf("\n=== Add New Student ===\n");
    
    // Auto-generate student ID
    new_student->student_id = next_student_id++;
    printf("Assigned Student ID: %d\n", new_student->student_id);
    
    printf("Enter student name: ");
    get_string_input(new_student->name, MAX_NAME_LENGTH);
    
    printf("Enter email: ");
    get_string_input(new_student->email, MAX_NAME_LENGTH);
    
    printf("Enter semester (1-8): ");
    new_student->semester = get_integer_input();
    
    // Input validation using if-else
    if (new_student->semester < 1 || new_student->semester > 8) {
        printf("Invalid semester! Setting to 1.\n");
        new_student->semester = 1;
    }
    
    printf("Enter GPA (0.0-4.0): ");
    new_student->gpa = get_float_input();
    
    if (new_student->gpa < 0.0 || new_student->gpa > 4.0) {
        printf("Invalid GPA! Setting to 0.0.\n");
        new_student->gpa = 0.0;
    }
    
    new_student->registered_courses_count = 0;
    
    // Initialize arrays using for loop
    for (int i = 0; i < 10; i++) {
        strcpy(new_student->registered_courses[i], "");
        new_student->registered_sections[i] = -1;
    }
    
    total_students++;
    printf("Student added successfully! Total students: %d\n", total_students);
}

void display_students(void) {
    if (total_students == 0) {
        printf("No students registered.\n");
        return;
    }
    
    printf("\n=== Student List ===\n");
    printf("%-6s %-25s %-30s %-8s %-6s %-8s\n", 
           "ID", "Name", "Email", "Semester", "GPA", "Courses");
    printf("--------------------------------------------------------------------------------\n");
    
    // Display all students using for loop
    for (int i = 0; i < total_students; i++) {
        print_student_details(&students[i]);
    }
}

Student* find_student_by_id(int id) {
    // Linear search using pointer arithmetic
    Student *student_ptr = students;
    
    for (int i = 0; i < total_students; i++) {
        if ((student_ptr + i)->student_id == id) {
            return student_ptr + i; // Return pointer to found student
        }
    }
    
    return NULL; // Student not found
}

void search_student_by_name(void) {
    if (total_students == 0) {
        printf("No students registered.\n");
        return;
    }

    printf("\n=== Search Student by Name ===\n");
    printf("Enter student name (or part of name): ");
    char search_name[MAX_NAME_LENGTH];
    get_string_input(search_name, MAX_NAME_LENGTH);

    int found = 0;
    printf("\n=== Search Results ===\n");
    printf("%-6s %-25s %-30s %-8s %-6s %-8s\n",
           "ID", "Name", "Email", "Semester", "GPA", "Courses");
    printf("--------------------------------------------------------------------------------\n");

    // Search using case-insensitive partial match
    for (int i = 0; i < total_students; i++) {
        // Convert both strings to lowercase for comparison
        char student_name_lower[MAX_NAME_LENGTH];
        char search_name_lower[MAX_NAME_LENGTH];

        strcpy(student_name_lower, students[i].name);
        strcpy(search_name_lower, search_name);

        // Convert to lowercase using for loop
        for (int j = 0; student_name_lower[j]; j++) {
            if (student_name_lower[j] >= 'A' && student_name_lower[j] <= 'Z') {
                student_name_lower[j] = student_name_lower[j] + 32;
            }
        }

        for (int j = 0; search_name_lower[j]; j++) {
            if (search_name_lower[j] >= 'A' && search_name_lower[j] <= 'Z') {
                search_name_lower[j] = search_name_lower[j] + 32;
            }
        }

        // Check if search term is found in student name
        if (strstr(student_name_lower, search_name_lower) != NULL) {
            print_student_details(&students[i]);
            found = 1;
        }
    }

    if (!found) {
        printf("No students found with name containing '%s'.\n", search_name);
    }
}

void update_student(void) {
    if (total_students == 0) {
        printf("No students available to update.\n");
        return;
    }

    printf("\n=== Update Student ===\n");
    display_students();

    printf("\nEnter student ID to update: ");
    int student_id = get_integer_input();

    Student *student = find_student_by_id(student_id);
    if (student == NULL) {
        printf("Student with ID %d not found.\n", student_id);
        return;
    }

    printf("\nCurrent student details:\n");
    printf("%-6s %-25s %-30s %-8s %-6s %-8s\n",
           "ID", "Name", "Email", "Semester", "GPA", "Courses");
    printf("--------------------------------------------------------------------------------\n");
    print_student_details(student);

    printf("\n=== Update Student Information ===\n");
    printf("1. Update Name\n");
    printf("2. Update Email\n");
    printf("3. Update Semester\n");
    printf("4. Update GPA\n");
    printf("5. Update All Information\n");
    printf("Enter choice (1-5): ");

    int choice = get_integer_input();

    switch(choice) {
        case 1:
            printf("Enter new name: ");
            get_string_input(student->name, MAX_NAME_LENGTH);
            break;
        case 2:
            printf("Enter new email: ");
            get_string_input(student->email, MAX_NAME_LENGTH);
            break;
        case 3:
            printf("Enter new semester (1-8): ");
            student->semester = get_integer_input();
            if (student->semester < 1 || student->semester > 8) {
                printf("Invalid semester! Setting to 1.\n");
                student->semester = 1;
            }
            break;
        case 4:
            printf("Enter new GPA (0.0-4.0): ");
            student->gpa = get_float_input();
            if (student->gpa < 0.0 || student->gpa > 4.0) {
                printf("Invalid GPA! Setting to 0.0.\n");
                student->gpa = 0.0;
            }
            break;
        case 5:
            printf("Enter new name: ");
            get_string_input(student->name, MAX_NAME_LENGTH);
            printf("Enter new email: ");
            get_string_input(student->email, MAX_NAME_LENGTH);
            printf("Enter new semester (1-8): ");
            student->semester = get_integer_input();
            if (student->semester < 1 || student->semester > 8) {
                printf("Invalid semester! Setting to 1.\n");
                student->semester = 1;
            }
            printf("Enter new GPA (0.0-4.0): ");
            student->gpa = get_float_input();
            if (student->gpa < 0.0 || student->gpa > 4.0) {
                printf("Invalid GPA! Setting to 0.0.\n");
                student->gpa = 0.0;
            }
            break;
        default:
            printf("Invalid choice!\n");
            return;
    }

    printf("Student updated successfully!\n");
    printf("\nUpdated student details:\n");
    printf("%-6s %-25s %-30s %-8s %-6s %-8s\n",
           "ID", "Name", "Email", "Semester", "GPA", "Courses");
    printf("--------------------------------------------------------------------------------\n");
    print_student_details(student);
}

void delete_student(void) {
    if (total_students == 0) {
        printf("No students available to delete.\n");
        return;
    }

    printf("\n=== Delete Student ===\n");
    display_students();

    printf("\nEnter student ID to delete: ");
    int student_id = get_integer_input();

    // Find student using linear search
    int student_index = -1;
    for (int i = 0; i < total_students; i++) {
        if (students[i].student_id == student_id) {
            student_index = i;
            break;
        }
    }

    if (student_index == -1) {
        printf("Student with ID %d not found.\n", student_id);
        return;
    }

    Student *student = &students[student_index];

    // Check if student is enrolled in any courses
    if (student->registered_courses_count > 0) {
        printf("Cannot delete student '%s' (ID: %d) because they are enrolled in %d course(s).\n",
               student->name, student_id, student->registered_courses_count);
        printf("Please drop the student from all courses first.\n");

        printf("\nEnrolled courses:\n");
        for (int i = 0; i < student->registered_courses_count; i++) {
            printf("- %s (Section: %s)\n",
                   student->registered_courses[i],
                   sections[student->registered_sections[i]].section_id);
        }
        return;
    }

    printf("\nStudent to be deleted:\n");
    printf("%-6s %-25s %-30s %-8s %-6s %-8s\n",
           "ID", "Name", "Email", "Semester", "GPA", "Courses");
    printf("--------------------------------------------------------------------------------\n");
    print_student_details(student);

    printf("\nAre you sure you want to delete this student? (y/n): ");
    char confirm[10];
    get_string_input(confirm, 10);

    if (confirm[0] != 'y' && confirm[0] != 'Y') {
        printf("Student deletion cancelled.\n");
        return;
    }

    // Remove student from all sections they might be enrolled in
    for (int i = 0; i < total_sections; i++) {
        for (int j = 0; j < sections[i].enrolled_count; j++) {
            if (sections[i].student_ids[j] == student_id) {
                // Shift remaining student IDs
                for (int k = j; k < sections[i].enrolled_count - 1; k++) {
                    sections[i].student_ids[k] = sections[i].student_ids[k + 1];
                }
                sections[i].enrolled_count--;
                break;
            }
        }
    }

    // Shift remaining students to fill the gap
    for (int i = student_index; i < total_students - 1; i++) {
        students[i] = students[i + 1];
    }

    total_students--;
    printf("Student with ID %d deleted successfully! Total students: %d\n", student_id, total_students);
}

void register_student_for_section(void) {
    if (total_students == 0) {
        printf("No students available.\n");
        return;
    }
    
    if (total_sections == 0) {
        printf("No sections available.\n");
        return;
    }
    
    printf("\n=== Register Student for Section ===\n");
    
    printf("Enter student ID: ");
    int student_id = get_integer_input();
    
    Student *student = find_student_by_id(student_id);
    if (student == NULL) {
        printf("Student with ID %d not found.\n", student_id);
        return;
    }
    
    printf("\nStudent: %s (ID: %d)\n", student->name, student->student_id);
    
    // Display available sections
    printf("\nAvailable sections:\n");
    display_sections();
    
    printf("\nEnter section ID to register: ");
    char section_id[MAX_CODE_LENGTH];
    get_string_input(section_id, MAX_CODE_LENGTH);
    
    // Find section using while loop
    int section_index = -1;
    int i = 0;
    while (i < total_sections) {
        if (strcmp(sections[i].section_id, section_id) == 0) {
            section_index = i;
            break;
        }
        i++;
    }
    
    if (section_index == -1) {
        printf("Section not found.\n");
        return;
    }
    
    Section *section = &sections[section_index];
    
    // Check if section is full
    if (is_section_full(section_index)) {
        printf("Section is full! Cannot register.\n");
        return;
    }
    
    // Check if student is already registered for this course
    for (int j = 0; j < student->registered_courses_count; j++) {
        if (strcmp(student->registered_courses[j], section->course_code) == 0) {
            printf("Student is already registered for this course.\n");
            return;
        }
    }
    
    // Check prerequisites
    if (!validate_prerequisites(student_id, section->course_code)) {
        printf("Student does not meet prerequisites for this course.\n");
        return;
    }
    
    // Check time conflicts
    if (!check_time_conflict(student_id, section->time_slot)) {
        printf("Time conflict detected with existing registrations.\n");
        return;
    }
    
    // Register student
    strcpy(student->registered_courses[student->registered_courses_count], section->course_code);
    student->registered_sections[student->registered_courses_count] = section_index;
    student->registered_courses_count++;
    
    // Add student to section
    section->student_ids[section->enrolled_count] = student_id;
    section->enrolled_count++;
    
    printf("Student successfully registered for section %s!\n", section_id);
}

void drop_student_from_section(void) {
    printf("\n=== Drop Student from Section ===\n");
    
    printf("Enter student ID: ");
    int student_id = get_integer_input();
    
    Student *student = find_student_by_id(student_id);
    if (student == NULL) {
        printf("Student with ID %d not found.\n", student_id);
        return;
    }
    
    if (student->registered_courses_count == 0) {
        printf("Student is not registered for any courses.\n");
        return;
    }
    
    printf("\nStudent: %s (ID: %d)\n", student->name, student->student_id);
    printf("Registered courses:\n");
    
    // Display student's registered courses using for loop
    for (int i = 0; i < student->registered_courses_count; i++) {
        printf("%d. %s (Section: %s)\n", 
               i + 1, 
               student->registered_courses[i],
               sections[student->registered_sections[i]].section_id);
    }
    
    printf("\nEnter course number to drop (1-%d): ", student->registered_courses_count);
    int course_choice = get_integer_input();
    
    if (course_choice < 1 || course_choice > student->registered_courses_count) {
        printf("Invalid choice.\n");
        return;
    }
    
    int course_index = course_choice - 1;
    int section_index = student->registered_sections[course_index];
    Section *section = &sections[section_index];
    
    // Remove student from section's student list
    for (int i = 0; i < section->enrolled_count; i++) {
        if (section->student_ids[i] == student_id) {
            // Shift remaining students using for loop
            for (int j = i; j < section->enrolled_count - 1; j++) {
                section->student_ids[j] = section->student_ids[j + 1];
            }
            section->enrolled_count--;
            break;
        }
    }
    
    // Remove course from student's registered courses
    for (int i = course_index; i < student->registered_courses_count - 1; i++) {
        strcpy(student->registered_courses[i], student->registered_courses[i + 1]);
        student->registered_sections[i] = student->registered_sections[i + 1];
    }
    student->registered_courses_count--;
    
    printf("Student successfully dropped from the course!\n");
}

// Validation functions
int validate_prerequisites(int student_id, char *course_code) {
    // Find the course
    Course *course = NULL;
    for (int i = 0; i < total_courses; i++) {
        if (strcmp(courses[i].course_code, course_code) == 0) {
            course = &courses[i];
            break;
        }
    }
    
    if (course == NULL) return 0;
    
    // If no prerequisites or "None", return true
    if (strcmp(course->prerequisites, "None") == 0 || 
        strcmp(course->prerequisites, "") == 0) {
        return 1;
    }
    
    // For simplicity, assume prerequisites are met if student is in semester 2 or higher
    Student *student = find_student_by_id(student_id);
    if (student != NULL && student->semester >= 2) {
        return 1;
    }
    
    return 0;
}

int check_time_conflict(int student_id, char *new_time_slot) {
    Student *student = find_student_by_id(student_id);
    if (student == NULL) return 0;
    
    // Check against all registered sections using for loop
    for (int i = 0; i < student->registered_courses_count; i++) {
        int section_index = student->registered_sections[i];
        if (strcmp(sections[section_index].time_slot, new_time_slot) == 0) {
            return 0; // Conflict found
        }
    }
    
    return 1; // No conflict
}

int is_section_full(int section_index) {
    if (section_index < 0 || section_index >= total_sections) return 1;
    
    return sections[section_index].enrolled_count >= sections[section_index].capacity;
}
