#include "course_system.h"

// Static variable for student ID generation
static int next_student_id = 1001;

void add_student(void) {
    if (total_students >= MAX_STUDENTS) {
        printf("Maximum student limit reached!\n");
        return;
    }
    
    Student *new_student = &students[total_students];
    
    printf("\n=== Add New Student ===\n");
    
    // Auto-generate student ID
    new_student->student_id = next_student_id++;
    printf("Assigned Student ID: %d\n", new_student->student_id);
    
    printf("Enter student name: ");
    get_string_input(new_student->name, MAX_NAME_LENGTH);
    
    printf("Enter email: ");
    get_string_input(new_student->email, MAX_NAME_LENGTH);
    
    printf("Enter semester (1-8): ");
    new_student->semester = get_integer_input();
    
    // Input validation using if-else
    if (new_student->semester < 1 || new_student->semester > 8) {
        printf("Invalid semester! Setting to 1.\n");
        new_student->semester = 1;
    }
    
    printf("Enter GPA (0.0-4.0): ");
    new_student->gpa = get_float_input();
    
    if (new_student->gpa < 0.0 || new_student->gpa > 4.0) {
        printf("Invalid GPA! Setting to 0.0.\n");
        new_student->gpa = 0.0;
    }
    
    new_student->registered_courses_count = 0;
    
    // Initialize arrays using for loop
    for (int i = 0; i < 10; i++) {
        strcpy(new_student->registered_courses[i], "");
        new_student->registered_sections[i] = -1;
    }
    
    total_students++;
    printf("Student added successfully! Total students: %d\n", total_students);
}

void display_students(void) {
    if (total_students == 0) {
        printf("No students registered.\n");
        return;
    }
    
    printf("\n=== Student List ===\n");
    printf("%-6s %-25s %-30s %-8s %-6s %-8s\n", 
           "ID", "Name", "Email", "Semester", "GPA", "Courses");
    printf("--------------------------------------------------------------------------------\n");
    
    // Display all students using for loop
    for (int i = 0; i < total_students; i++) {
        print_student_details(&students[i]);
    }
}

Student* find_student_by_id(int id) {
    // Linear search using pointer arithmetic
    Student *student_ptr = students;
    
    for (int i = 0; i < total_students; i++) {
        if ((student_ptr + i)->student_id == id) {
            return student_ptr + i; // Return pointer to found student
        }
    }
    
    return NULL; // Student not found
}

void register_student_for_section(void) {
    if (total_students == 0) {
        printf("No students available.\n");
        return;
    }
    
    if (total_sections == 0) {
        printf("No sections available.\n");
        return;
    }
    
    printf("\n=== Register Student for Section ===\n");
    
    printf("Enter student ID: ");
    int student_id = get_integer_input();
    
    Student *student = find_student_by_id(student_id);
    if (student == NULL) {
        printf("Student with ID %d not found.\n", student_id);
        return;
    }
    
    printf("\nStudent: %s (ID: %d)\n", student->name, student->student_id);
    
    // Display available sections
    printf("\nAvailable sections:\n");
    display_sections();
    
    printf("\nEnter section ID to register: ");
    char section_id[MAX_CODE_LENGTH];
    get_string_input(section_id, MAX_CODE_LENGTH);
    
    // Find section using while loop
    int section_index = -1;
    int i = 0;
    while (i < total_sections) {
        if (strcmp(sections[i].section_id, section_id) == 0) {
            section_index = i;
            break;
        }
        i++;
    }
    
    if (section_index == -1) {
        printf("Section not found.\n");
        return;
    }
    
    Section *section = &sections[section_index];
    
    // Check if section is full
    if (is_section_full(section_index)) {
        printf("Section is full! Cannot register.\n");
        return;
    }
    
    // Check if student is already registered for this course
    for (int j = 0; j < student->registered_courses_count; j++) {
        if (strcmp(student->registered_courses[j], section->course_code) == 0) {
            printf("Student is already registered for this course.\n");
            return;
        }
    }
    
    // Check prerequisites
    if (!validate_prerequisites(student_id, section->course_code)) {
        printf("Student does not meet prerequisites for this course.\n");
        return;
    }
    
    // Check time conflicts
    if (!check_time_conflict(student_id, section->time_slot)) {
        printf("Time conflict detected with existing registrations.\n");
        return;
    }
    
    // Register student
    strcpy(student->registered_courses[student->registered_courses_count], section->course_code);
    student->registered_sections[student->registered_courses_count] = section_index;
    student->registered_courses_count++;
    
    // Add student to section
    section->student_ids[section->enrolled_count] = student_id;
    section->enrolled_count++;
    
    printf("Student successfully registered for section %s!\n", section_id);
}

void drop_student_from_section(void) {
    printf("\n=== Drop Student from Section ===\n");
    
    printf("Enter student ID: ");
    int student_id = get_integer_input();
    
    Student *student = find_student_by_id(student_id);
    if (student == NULL) {
        printf("Student with ID %d not found.\n", student_id);
        return;
    }
    
    if (student->registered_courses_count == 0) {
        printf("Student is not registered for any courses.\n");
        return;
    }
    
    printf("\nStudent: %s (ID: %d)\n", student->name, student->student_id);
    printf("Registered courses:\n");
    
    // Display student's registered courses using for loop
    for (int i = 0; i < student->registered_courses_count; i++) {
        printf("%d. %s (Section: %s)\n", 
               i + 1, 
               student->registered_courses[i],
               sections[student->registered_sections[i]].section_id);
    }
    
    printf("\nEnter course number to drop (1-%d): ", student->registered_courses_count);
    int course_choice = get_integer_input();
    
    if (course_choice < 1 || course_choice > student->registered_courses_count) {
        printf("Invalid choice.\n");
        return;
    }
    
    int course_index = course_choice - 1;
    int section_index = student->registered_sections[course_index];
    Section *section = &sections[section_index];
    
    // Remove student from section's student list
    for (int i = 0; i < section->enrolled_count; i++) {
        if (section->student_ids[i] == student_id) {
            // Shift remaining students using for loop
            for (int j = i; j < section->enrolled_count - 1; j++) {
                section->student_ids[j] = section->student_ids[j + 1];
            }
            section->enrolled_count--;
            break;
        }
    }
    
    // Remove course from student's registered courses
    for (int i = course_index; i < student->registered_courses_count - 1; i++) {
        strcpy(student->registered_courses[i], student->registered_courses[i + 1]);
        student->registered_sections[i] = student->registered_sections[i + 1];
    }
    student->registered_courses_count--;
    
    printf("Student successfully dropped from the course!\n");
}

// Validation functions
int validate_prerequisites(int student_id, char *course_code) {
    // Find the course
    Course *course = NULL;
    for (int i = 0; i < total_courses; i++) {
        if (strcmp(courses[i].course_code, course_code) == 0) {
            course = &courses[i];
            break;
        }
    }
    
    if (course == NULL) return 0;
    
    // If no prerequisites or "None", return true
    if (strcmp(course->prerequisites, "None") == 0 || 
        strcmp(course->prerequisites, "") == 0) {
        return 1;
    }
    
    // For simplicity, assume prerequisites are met if student is in semester 2 or higher
    Student *student = find_student_by_id(student_id);
    if (student != NULL && student->semester >= 2) {
        return 1;
    }
    
    return 0;
}

int check_time_conflict(int student_id, char *new_time_slot) {
    Student *student = find_student_by_id(student_id);
    if (student == NULL) return 0;
    
    // Check against all registered sections using for loop
    for (int i = 0; i < student->registered_courses_count; i++) {
        int section_index = student->registered_sections[i];
        if (strcmp(sections[section_index].time_slot, new_time_slot) == 0) {
            return 0; // Conflict found
        }
    }
    
    return 1; // No conflict
}

int is_section_full(int section_index) {
    if (section_index < 0 || section_index >= total_sections) return 1;
    
    return sections[section_index].enrolled_count >= sections[section_index].capacity;
}
