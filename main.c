#include "course_system.h"

// Global variable definitions
int total_courses = 0;
int total_students = 0;
int total_sections = 0;

// Global arrays
Course courses[MAX_COURSES];
Section sections[MAX_SECTIONS];
Student students[MAX_STUDENTS];

// Static variable for menu choice tracking
static int menu_access_count = 0;

int main(void) {
    // Register variable for frequently used loop counter
    register int choice;
    
    printf("=== University Pre-Registration & Course Selection System ===\n");
    printf("Welcome to the Course Registration System!\n\n");
    
    // Load existing data from file
    load_data_from_file();
    
    // Main program loop using do-while
    do {
        menu_access_count++; // Static variable demonstration
        display_menu();
        choice = get_integer_input();
        
        // Using switch statement with if-else logic
        switch(choice) {
            case 1:
                add_course();
                break;
            case 2:
                display_courses();
                break;
            case 3:
                if (total_courses > 0) {
                    printf("\nChoose sorting method:\n");
                    printf("1. Sort by Course Name (Bubble Sort)\n");
                    printf("2. Sort by Credits (Selection Sort)\n");
                    printf("Enter choice: ");
                    int sort_choice = get_integer_input();
                    
                    if (sort_choice == 1) {
                        sort_courses_by_name_bubble();
                        printf("Courses sorted by name using Bubble Sort!\n");
                    } else if (sort_choice == 2) {
                        sort_courses_by_credits_selection();
                        printf("Courses sorted by credits using Selection Sort!\n");
                    } else {
                        printf("Invalid choice!\n");
                    }
                } else {
                    printf("No courses available to sort!\n");
                }
                break;
            case 4:
                add_section();
                break;
            case 5:
                display_sections();
                break;
            case 6:
                add_student();
                break;
            case 7:
                display_students();
                break;
            case 8:
                register_student_for_section();
                break;
            case 9:
                drop_student_from_section();
                break;
            case 10: {
                printf("Enter course code to search: ");
                char search_code[MAX_CODE_LENGTH];
                get_string_input(search_code, MAX_CODE_LENGTH);
                search_course_by_code(search_code);
                break;
            }
            case 11: {
                printf("Enter course code to view sections: ");
                char course_code[MAX_CODE_LENGTH];
                get_string_input(course_code, MAX_CODE_LENGTH);
                display_sections_for_course(course_code);
                break;
            }
            case 12:
                // Demonstrate pointer to pointer functionality
                if (total_courses > 0) {
                    Course **course_ptrs = malloc(total_courses * sizeof(Course*));
                    for (int i = 0; i < total_courses; i++) {
                        course_ptrs[i] = &courses[i];
                    }
                    printf("\nDisplaying courses using pointer to pointer:\n");
                    display_courses_using_pointer_to_pointer(course_ptrs, total_courses);
                    free(course_ptrs);
                } else {
                    printf("No courses available!\n");
                }
                break;
            case 13:
                save_data_to_file();
                printf("Data saved successfully!\n");
                break;
            case 14:
                printf("Menu has been accessed %d times.\n", menu_access_count);
                break;
            case 0:
                save_data_to_file();
                printf("Thank you for using the Course Registration System!\n");
                break;
            default:
                printf("Invalid choice! Please try again.\n");
        }
        
        if (choice != 0) {
            printf("\nPress Enter to continue...");
            getchar();
        }
        
    } while (choice != 0);
    
    return 0;
}

void display_menu(void) {
    printf("\n==================================================\n");
    printf("COURSE REGISTRATION SYSTEM MENU\n");
    printf("==================================================\n");
    printf("Course Management:\n");
    printf("1. Add Course\n");
    printf("2. Display All Courses\n");
    printf("3. Sort Courses\n");
    printf("4. Add Section\n");
    printf("5. Display All Sections\n");
    printf("\nStudent Management:\n");
    printf("6. Add Student\n");
    printf("7. Display All Students\n");
    printf("8. Register Student for Section\n");
    printf("9. Drop Student from Section\n");
    printf("\nSearch & View:\n");
    printf("10. Search Course by Code\n");
    printf("11. View Sections for Course\n");
    printf("12. Advanced Display (Pointer Demo)\n");
    printf("\nSystem:\n");
    printf("13. Save Data\n");
    printf("14. Show System Stats\n");
    printf("0. Exit\n");
    printf("==================================================\n");
    printf("Enter your choice: ");
}
