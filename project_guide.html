<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Pre-Registration System - C Programming Project Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .step {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .warning {
            background-color: #f8d7da;
            padding: 10px;
            border-left: 4px solid #dc3545;
            margin: 10px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .topic-coverage {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .menu-option {
            background-color: #f1f3f4;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #2980b9;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 University Pre-Registration & Course Selection System</h1>
        <p style="text-align: center; font-size: 18px; color: #666;">
            A Comprehensive C Programming Educational Project
        </p>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li><a href="#overview">1. Project Overview</a></li>
                <li><a href="#topics">2. C Programming Topics Covered</a></li>
                <li><a href="#structure">3. Project Structure</a></li>
                <li><a href="#compilation">4. Compilation Guide</a></li>
                <li><a href="#demo">5. Step-by-Step Demo</a></li>
                <li><a href="#code-analysis">6. Important Code Analysis</a></li>
                <li><a href="#features">7. Key Features</a></li>
                <li><a href="#learning">8. Learning Outcomes</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2>🎯 1. Project Overview</h2>
            <p>This project is a comprehensive <strong>University Course Registration System</strong> built in C that demonstrates all fundamental C programming concepts through a practical, real-world application.</p>
            
            <div class="highlight">
                <strong>🎓 Educational Purpose:</strong> This project covers ALL the essential C programming topics required for a pre-registration course, including data types, operators, control structures, functions, arrays, sorting algorithms, pointers, and structures.
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📚 Course Management (Full CRUD)</h4>
                    <ul>
                        <li><strong>CREATE:</strong> Add new courses</li>
                        <li><strong>READ:</strong> Display course catalog</li>
                        <li><strong>UPDATE:</strong> Modify course information</li>
                        <li><strong>DELETE:</strong> Remove courses (with validation)</li>
                        <li><strong>SEARCH:</strong> Find courses by code</li>
                        <li><strong>SORT:</strong> Bubble & Selection Sort algorithms</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>👥 Student Management (Full CRUD)</h4>
                    <ul>
                        <li><strong>CREATE:</strong> Student registration</li>
                        <li><strong>READ:</strong> Display all students</li>
                        <li><strong>UPDATE:</strong> Modify student information</li>
                        <li><strong>DELETE:</strong> Remove students (with enrollment check)</li>
                        <li><strong>SEARCH:</strong> Find students by name (case-insensitive)</li>
                        <li><strong>ENROLL:</strong> Course enrollment with validation</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>🏫 Section Management (Full CRUD)</h4>
                    <ul>
                        <li><strong>CREATE:</strong> Create course sections</li>
                        <li><strong>READ:</strong> Display all sections</li>
                        <li><strong>UPDATE:</strong> Modify section details</li>
                        <li><strong>DELETE:</strong> Remove sections (with enrollment check)</li>
                        <li><strong>MANAGE:</strong> Time slot and capacity management</li>
                        <li><strong>TRACK:</strong> Enrollment tracking</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="topics">
            <h2>📖 2. C Programming Topics Covered</h2>
            
            <div class="topic-coverage">
                <h3>✅ Complete Coverage of All Required Topics:</h3>
                <table>
                    <tr>
                        <th>Topic</th>
                        <th>Implementation</th>
                        <th>File Location</th>
                    </tr>
                    <tr>
                        <td><strong>Data Types</strong></td>
                        <td>int, char, float, arrays, structures</td>
                        <td>course_system.h</td>
                    </tr>
                    <tr>
                        <td><strong>Operators</strong></td>
                        <td>Arithmetic, logical, relational, assignment</td>
                        <td>All .c files</td>
                    </tr>
                    <tr>
                        <td><strong>I/O Functions</strong></td>
                        <td>printf, scanf, fgets, fprintf, fscanf</td>
                        <td>utils.c, main.c</td>
                    </tr>
                    <tr>
                        <td><strong>If-else</strong></td>
                        <td>Input validation, menu logic</td>
                        <td>All .c files</td>
                    </tr>
                    <tr>
                        <td><strong>For Loop</strong></td>
                        <td>Array iteration, sorting algorithms</td>
                        <td>course_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>While Loop</strong></td>
                        <td>Search operations, file reading</td>
                        <td>student_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>Do-while Loop</strong></td>
                        <td>Main menu system, input validation</td>
                        <td>main.c, utils.c</td>
                    </tr>
                    <tr>
                        <td><strong>Functions</strong></td>
                        <td>25+ functions with various parameters</td>
                        <td>All .c files</td>
                    </tr>
                    <tr>
                        <td><strong>Variables</strong></td>
                        <td>Global, local, static, register</td>
                        <td>All .c files</td>
                    </tr>
                    <tr>
                        <td><strong>Arrays</strong></td>
                        <td>1D, 2D arrays, arrays of structures</td>
                        <td>course_system.h</td>
                    </tr>
                    <tr>
                        <td><strong>Bubble Sort</strong></td>
                        <td>Sort courses by name</td>
                        <td>course_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>Selection Sort</strong></td>
                        <td>Sort courses by credits</td>
                        <td>course_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>Pointer Arithmetic</strong></td>
                        <td>Array access using pointers</td>
                        <td>student_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>Arrays & Pointers</strong></td>
                        <td>Pointer-based array traversal</td>
                        <td>course_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>Pointer to Pointer</strong></td>
                        <td>Advanced pointer operations</td>
                        <td>course_management.c</td>
                    </tr>
                    <tr>
                        <td><strong>Structures</strong></td>
                        <td>Course, Student, Section structures</td>
                        <td>course_system.h</td>
                    </tr>
                </table>
            </div>
        </section>

        <section id="structure">
            <h2>📁 3. Project Structure</h2>
            
            <div class="code-block">
pre-registration-app/
├── main.c                 # Main program and menu system
├── course_system.h        # Header file with all declarations
├── course_management.c    # Course and section management
├── student_management.c   # Student operations
├── utils.c               # Utility functions and file I/O
├── Makefile              # Build configuration
├── README.md             # Project documentation
├── course_data.txt       # Data persistence file
└── project_guide.html    # This guide
            </div>

            <h3>📄 File Descriptions:</h3>
            <ul>
                <li><strong>main.c:</strong> Contains the main function, menu system, and program flow control</li>
                <li><strong>course_system.h:</strong> Header file with structure definitions, function declarations, and constants</li>
                <li><strong>course_management.c:</strong> Functions for managing courses and sections, including sorting algorithms</li>
                <li><strong>student_management.c:</strong> Student-related operations, registration, and validation</li>
                <li><strong>utils.c:</strong> Utility functions for I/O, file operations, and helper functions</li>
                <li><strong>course_data.txt:</strong> Data file for persistence (created automatically)</li>
            </ul>
        </section>

        <section id="compilation">
            <h2>🔨 4. Compilation Guide</h2>
            
            <div class="step">
                <h4>Step 1: Manual Compilation</h4>
                <div class="code-block">
gcc -Wall -Wextra -std=c99 -g main.c course_management.c student_management.c utils.c -o course_system
                </div>
                <p><strong>Explanation:</strong></p>
                <ul>
                    <li><code>-Wall -Wextra</code>: Enable all warnings for better code quality</li>
                    <li><code>-std=c99</code>: Use C99 standard</li>
                    <li><code>-g</code>: Include debugging information</li>
                    <li><code>-o course_system</code>: Output executable name</li>
                </ul>
            </div>

            <div class="step">
                <h4>Step 2: Using Makefile (if available)</h4>
                <div class="code-block">
make
# or
make run    # Compile and run
make clean  # Clean build files
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ Note:</strong> If you get a "make: command not found" error, use the manual compilation method above.
            </div>
        </section>

        <section id="demo">
            <h2>🎮 5. Step-by-Step Demo Guide</h2>

            <div class="step">
                <h4>Step 1: Run the Program</h4>
                <div class="code-block">
./course_system
                </div>
                <p>You'll see the main menu with options 1-21 and 0 to exit, including full CRUD operations.</p>
            </div>

            <div class="step">
                <h4>Step 2: View Existing Data</h4>
                <div class="menu-option">Menu Option 2: Display All Courses</div>
                <p>This shows the pre-loaded sample courses:</p>
                <div class="code-block">
Code       Name                           Credits  Prerequisites        Instructor
--------------------------------------------------------------------------------
CS101      Introduction to Programming    3        None                 Dr. Smith
CS102      Data Structures                4        CS101                Dr. Johnson
MATH101    Calculus I                     4        None                 Dr. Brown
                </div>
            </div>

            <div class="step">
                <h4>Step 3: View Sections</h4>
                <div class="menu-option">Menu Option 7: Display All Sections</div>
                <p>Shows available course sections with time slots and capacity:</p>
                <div class="code-block">
Section    Course     Time Slot            Room            Capacity Enrolled
--------------------------------------------------------------------------------
A          CS101      MWF 09:00-10:00      Room 101        25       0
B          CS102      TTH 10:00-11:30      Room 102        20       0
C          MATH101    MWF 11:00-12:00      Room 201        30       0
                </div>
            </div>

            <div class="step">
                <h4>Step 4: Add a New Student (CREATE)</h4>
                <div class="menu-option">Menu Option 10: Add Student</div>
                <p>Follow the prompts to add a student:</p>
                <div class="code-block">
=== Add New Student ===
Assigned Student ID: 1001
Enter student name: John Doe
Enter email: <EMAIL>
Enter semester (1-8): 2
Enter GPA (0.0-4.0): 3.5
Student added successfully! Total students: 1
                </div>
            </div>

            <div class="step">
                <h4>Step 4a: Update Student Information (UPDATE)</h4>
                <div class="menu-option">Menu Option 12: Update Student</div>
                <p>Modify existing student information:</p>
                <div class="code-block">
=== Update Student ===
Enter student ID to update: 1001
Current student details: [displays current info]

=== Update Student Information ===
1. Update Name
2. Update Email
3. Update Semester
4. Update GPA
5. Update All Information
Enter choice (1-5): 2
Enter new email: <EMAIL>
Student updated successfully!
                </div>
            </div>

            <div class="step">
                <h4>Step 4b: Search Student by Name (SEARCH)</h4>
                <div class="menu-option">Menu Option 17: Search Student by Name</div>
                <p>Find students using partial name matching:</p>
                <div class="code-block">
=== Search Student by Name ===
Enter student name (or part of name): john
=== Search Results ===
ID     Name                      Email                          Semester GPA    Courses
--------------------------------------------------------------------------------
1001   <USER> <GROUP>                  <EMAIL> 2        3.50   0
                </div>
            </div>

            <div class="step">
                <h4>Step 5: Register Student for a Course</h4>
                <div class="menu-option">Menu Option 14: Register Student for Section</div>
                <p>Register the student for a course section:</p>
                <div class="code-block">
=== Register Student for Section ===
Enter student ID: 1001
Student: John Doe (ID: 1001)

Available sections:
[Section list displays]

Enter section ID to register: A
Student successfully registered for section A!
                </div>
            </div>

            <div class="step">
                <h4>Step 6: Test Sorting Algorithms</h4>
                <div class="menu-option">Menu Option 5: Sort Courses</div>
                <p>Choose between two sorting methods:</p>
                <div class="code-block">
Choose sorting method:
1. Sort by Course Name (Bubble Sort)
2. Sort by Credits (Selection Sort)
Enter choice: 1
Courses sorted by name using Bubble Sort!
                </div>
            </div>

            <div class="step">
                <h4>Step 7: Search for a Course</h4>
                <div class="menu-option">Menu Option 16: Search Course by Code</div>
                <div class="code-block">
Enter course code to search: CS101
=== Course Found ===
Code       Name                           Credits  Prerequisites        Instructor
--------------------------------------------------------------------------------
CS101      Introduction to Programming    3        None                 Dr. Smith
                </div>
            </div>

            <div class="step">
                <h4>Step 8: Advanced Pointer Demo</h4>
                <div class="menu-option">Menu Option 19: Advanced Display (Pointer Demo)</div>
                <p>Demonstrates pointer to pointer functionality:</p>
                <div class="code-block">
Displaying courses using pointer to pointer:
[Courses displayed using advanced pointer operations]
                </div>
            </div>

            <div class="step">
                <h4>Step 9: Save Data</h4>
                <div class="menu-option">Menu Option 20: Save Data</div>
                <div class="code-block">
Data saved successfully!
                </div>
                <p>This saves all data to <code>course_data.txt</code> for persistence.</p>
            </div>

            <div class="step">
                <h4>Step 10: Exit Program</h4>
                <div class="menu-option">Menu Option 0: Exit</div>
                <div class="code-block">
Thank you for using the Course Registration System!
                </div>
            </div>
        </section>

        <section id="code-analysis">
            <h2>🔍 6. Important Code Analysis</h2>

            <h3>📋 Structure Definitions</h3>
            <p>The project uses three main structures to represent the data:</p>

            <div class="code-block">
typedef struct {
    char course_code[MAX_CODE_LENGTH];     // Course identifier
    char course_name[MAX_NAME_LENGTH];     // Full course name
    int credits;                           // Credit hours (1-4)
    char prerequisites[MAX_NAME_LENGTH];   // Required prerequisites
    char instructor[MAX_NAME_LENGTH];      // Instructor name
} Course;
            </div>

            <div class="highlight">
                <strong>Key Concepts Demonstrated:</strong>
                <ul>
                    <li><strong>typedef struct:</strong> Creating custom data types</li>
                    <li><strong>char arrays:</strong> String storage with fixed sizes</li>
                    <li><strong>int data type:</strong> Numeric data storage</li>
                    <li><strong>Constants:</strong> Using #define for array sizes</li>
                </ul>
            </div>

            <h3>🔄 Bubble Sort Implementation</h3>
            <div class="code-block">
void sort_courses_by_name_bubble(void) {
    if (total_courses <= 1) return;

    // Bubble sort using nested for loops
    for (int i = 0; i < total_courses - 1; i++) {
        for (int j = 0; j < total_courses - i - 1; j++) {
            // Compare course names using strcmp
            if (strcmp(courses[j].course_name, courses[j + 1].course_name) > 0) {
                swap_courses(&courses[j], &courses[j + 1]);
            }
        }
    }
}
            </div>

            <div class="highlight">
                <strong>Concepts Demonstrated:</strong>
                <ul>
                    <li><strong>Nested for loops:</strong> Double iteration for comparison</li>
                    <li><strong>String comparison:</strong> Using strcmp() function</li>
                    <li><strong>Function calls:</strong> Calling swap_courses() function</li>
                    <li><strong>Pointer passing:</strong> Passing addresses with &</li>
                    <li><strong>Algorithm logic:</strong> Bubble sort optimization</li>
                </ul>
            </div>

            <h3>👉 Pointer to Pointer Usage</h3>
            <div class="code-block">
// In main.c - Menu option 12
Course **course_ptrs = malloc(total_courses * sizeof(Course*));
for (int i = 0; i < total_courses; i++) {
    course_ptrs[i] = &courses[i];  // Point to each course
}
display_courses_using_pointer_to_pointer(course_ptrs, total_courses);
free(course_ptrs);  // Clean up memory
            </div>

            <div class="code-block">
// In course_management.c
void display_courses_using_pointer_to_pointer(Course **course_ptrs, int count) {
    for (int i = 0; i < count; i++) {
        Course *current_course = *(course_ptrs + i); // Pointer arithmetic
        print_course_details(current_course);
    }
}
            </div>

            <div class="highlight">
                <strong>Advanced Concepts:</strong>
                <ul>
                    <li><strong>Dynamic memory allocation:</strong> malloc() and free()</li>
                    <li><strong>Pointer to pointer:</strong> Course **course_ptrs</li>
                    <li><strong>Pointer arithmetic:</strong> *(course_ptrs + i)</li>
                    <li><strong>Memory management:</strong> Proper cleanup with free()</li>
                </ul>
            </div>

            <h3>🔍 Linear Search with Pointer Arithmetic</h3>
            <div class="code-block">
Student* find_student_by_id(int id) {
    // Linear search using pointer arithmetic
    Student *student_ptr = students;

    for (int i = 0; i < total_students; i++) {
        if ((student_ptr + i)->student_id == id) {
            return student_ptr + i; // Return pointer to found student
        }
    }

    return NULL; // Student not found
}
            </div>

            <div class="highlight">
                <strong>Concepts Demonstrated:</strong>
                <ul>
                    <li><strong>Pointer arithmetic:</strong> (student_ptr + i)</li>
                    <li><strong>Structure member access:</strong> Using -> operator</li>
                    <li><strong>Return values:</strong> Returning pointers</li>
                    <li><strong>NULL pointer:</strong> Indicating failure</li>
                </ul>
            </div>
        </section>

        <section id="features">
            <h2>🚀 7. Key Features</h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>📁 File I/O Operations</h4>
                    <div class="code-block">
void save_data_to_file(void) {
    FILE *file = fopen("course_data.txt", "w");
    if (file == NULL) {
        printf("Error: Could not open file for writing.\n");
        return;
    }

    // Save courses using fprintf
    fprintf(file, "COURSES %d\n", total_courses);
    for (int i = 0; i < total_courses; i++) {
        fprintf(file, "%s|%s|%d|%s|%s\n",
                courses[i].course_code,
                courses[i].course_name,
                courses[i].credits,
                courses[i].prerequisites,
                courses[i].instructor);
    }

    fclose(file);
}
                    </div>
                    <div class="highlight">
                        <strong>File I/O Concepts:</strong>
                        <ul>
                            <li><strong>File pointer:</strong> FILE *file</li>
                            <li><strong>File opening:</strong> fopen() with mode "w"</li>
                            <li><strong>Error checking:</strong> Checking for NULL</li>
                            <li><strong>Formatted output:</strong> fprintf() to file</li>
                            <li><strong>File closing:</strong> fclose() for cleanup</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <h4>🔄 Input Validation with Do-While</h4>
                    <div class="code-block">
int get_integer_input(void) {
    int value;
    int result;

    // Input validation using do-while loop
    do {
        result = scanf("%d", &value);
        if (result != 1) {
            printf("Invalid input! Please enter a number: ");
            clear_input_buffer();
            input_validation_errors++; // Static variable increment
        }
    } while (result != 1);

    clear_input_buffer();
    return value;
}
                    </div>
                    <div class="highlight">
                        <strong>Input Validation Concepts:</strong>
                        <ul>
                            <li><strong>Do-while loop:</strong> Ensures at least one execution</li>
                            <li><strong>scanf return value:</strong> Checking for successful input</li>
                            <li><strong>Static variables:</strong> input_validation_errors counter</li>
                            <li><strong>Buffer clearing:</strong> Preventing input issues</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <h4>🔍 Selection Sort Algorithm</h4>
                    <div class="code-block">
void sort_courses_by_credits_selection(void) {
    if (total_courses <= 1) return;

    // Selection sort using nested loops
    for (int i = 0; i < total_courses - 1; i++) {
        int min_index = i;

        // Find minimum element using for loop
        for (int j = i + 1; j < total_courses; j++) {
            if (courses[j].credits < courses[min_index].credits) {
                min_index = j;
            }
        }

        // Swap if minimum is not at current position
        if (min_index != i) {
            swap_courses(&courses[i], &courses[min_index]);
        }
    }
}
                    </div>
                    <div class="highlight">
                        <strong>Selection Sort Concepts:</strong>
                        <ul>
                            <li><strong>Nested loops:</strong> Outer loop for position, inner for finding minimum</li>
                            <li><strong>Index tracking:</strong> min_index variable</li>
                            <li><strong>Conditional swapping:</strong> Only swap when necessary</li>
                            <li><strong>Algorithm efficiency:</strong> O(n²) time complexity</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <h4>📊 Variable Types Demonstration</h4>
                    <div class="code-block">
// Global variables
int total_courses = 0;
int total_students = 0;
Course courses[MAX_COURSES];
Student students[MAX_STUDENTS];

// Static variables
static int next_student_id = 1001;
static int menu_access_count = 0;

// Register variable in main()
register int choice;

// Local variables in functions
void add_course(void) {
    Course *new_course = &courses[total_courses]; // Local pointer
    int i; // Local loop counter
    // ...
}
                    </div>
                    <div class="highlight">
                        <strong>Variable Types:</strong>
                        <ul>
                            <li><strong>Global:</strong> Accessible throughout the program</li>
                            <li><strong>Static:</strong> Retains value between function calls</li>
                            <li><strong>Register:</strong> Suggests CPU register storage</li>
                            <li><strong>Local:</strong> Function scope only</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="learning">
            <h2>🎓 8. Learning Outcomes</h2>

            <div class="topic-coverage">
                <h3>🎯 What Students Will Learn:</h3>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🔧 Programming Fundamentals</h4>
                        <ul>
                            <li>Modular programming with multiple files</li>
                            <li>Header file organization</li>
                            <li>Function declarations and definitions</li>
                            <li>Proper code structure and organization</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h4>📊 Data Management</h4>
                        <ul>
                            <li>Structure design and usage</li>
                            <li>Array manipulation and indexing</li>
                            <li>String handling and validation</li>
                            <li>Data persistence with file I/O</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h4>🧮 Algorithm Implementation</h4>
                        <ul>
                            <li>Bubble sort and selection sort</li>
                            <li>Linear search algorithms</li>
                            <li>Time complexity understanding</li>
                            <li>Algorithm comparison and analysis</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h4>👉 Advanced Pointer Concepts</h4>
                        <ul>
                            <li>Pointer arithmetic and manipulation</li>
                            <li>Pointer to pointer operations</li>
                            <li>Dynamic memory allocation</li>
                            <li>Memory management best practices</li>
                        </ul>
                    </div>
                </div>

                <h3>💡 Practical Skills Gained:</h3>
                <ul>
                    <li><strong>Real-world application development:</strong> Building a complete system</li>
                    <li><strong>Problem-solving skills:</strong> Implementing business logic and validation</li>
                    <li><strong>Code organization:</strong> Structuring large programs effectively</li>
                    <li><strong>Debugging techniques:</strong> Finding and fixing programming errors</li>
                    <li><strong>User interface design:</strong> Creating intuitive menu systems</li>
                    <li><strong>Data validation:</strong> Ensuring program robustness</li>
                </ul>

                <div class="warning">
                    <strong>🎯 Perfect for:</strong>
                    <ul>
                        <li>Computer Science students learning C programming</li>
                        <li>Pre-registration course requirements</li>
                        <li>Programming fundamentals courses</li>
                        <li>Data structures and algorithms introduction</li>
                        <li>Software development methodology learning</li>
                    </ul>
                </div>
            </div>

            <h3>🏆 Project Achievements</h3>
            <table>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>Total Lines of Code</td>
                    <td>1000+</td>
                    <td>Comprehensive implementation</td>
                </tr>
                <tr>
                    <td>Functions Implemented</td>
                    <td>25+</td>
                    <td>Modular design approach</td>
                </tr>
                <tr>
                    <td>Data Structures</td>
                    <td>3 Main Structures</td>
                    <td>Course, Student, Section</td>
                </tr>
                <tr>
                    <td>Sorting Algorithms</td>
                    <td>2 Implementations</td>
                    <td>Bubble Sort & Selection Sort</td>
                </tr>
                <tr>
                    <td>C Topics Covered</td>
                    <td>100%</td>
                    <td>All required fundamentals</td>
                </tr>
                <tr>
                    <td>File Operations</td>
                    <td>Complete I/O</td>
                    <td>Save/Load functionality</td>
                </tr>
            </table>

            <div class="highlight">
                <h4>🎉 Conclusion</h4>
                <p>This University Pre-Registration & Course Selection System serves as a comprehensive educational tool that demonstrates all fundamental C programming concepts through a practical, real-world application. Students gain hands-on experience with data structures, algorithms, file operations, and software design principles while building a functional system they can understand, modify, and extend.</p>

                <p><strong>The project successfully bridges the gap between theoretical knowledge and practical application, making it an ideal learning resource for C programming education.</strong></p>
            </div>
        </section>

    </div>
</body>
</html>
