# Makefile for Pre-Registration Course Selection System
# Demonstrates compilation of multiple C files

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
TARGET = course_system
SOURCES = main.c course_management.c student_management.c utils.c
OBJECTS = $(SOURCES:.c=.o)
HEADER = course_system.h

# Default target
all: $(TARGET)

# Link object files to create executable
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET)
	@echo "Build successful! Run with: ./$(TARGET)"

# Compile source files to object files
%.o: %.c $(HEADER)
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET) course_data.txt
	@echo "Clean completed."

# Install (copy to system directory - optional)
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/
	@echo "Installed to /usr/local/bin/"

# Run the program
run: $(TARGET)
	./$(TARGET)

# Debug build
debug: CFLAGS += -DDEBUG
debug: $(TARGET)

# Create sample data for testing
sample_data:
	@echo "Creating sample data..."
	@echo "COURSES 3" > course_data.txt
	@echo "CS101|Introduction to Programming|3|None|Dr. Smith" >> course_data.txt
	@echo "CS102|Data Structures|4|CS101|Dr. Johnson" >> course_data.txt
	@echo "MATH101|Calculus I|4|None|Dr. Brown" >> course_data.txt
	@echo "SECTIONS 3" >> course_data.txt
	@echo "A|CS101|MWF 09:00-10:00|Room 101|25|0" >> course_data.txt
	@echo "B|CS102|TTH 10:00-11:30|Room 102|20|0" >> course_data.txt
	@echo "C|MATH101|MWF 11:00-12:00|Room 201|30|0" >> course_data.txt
	@echo "STUDENTS 0" >> course_data.txt
	@echo "Sample data created in course_data.txt"

# Help target
help:
	@echo "Available targets:"
	@echo "  all         - Build the program (default)"
	@echo "  clean       - Remove build files"
	@echo "  run         - Build and run the program"
	@echo "  debug       - Build with debug flags"
	@echo "  sample_data - Create sample data file"
	@echo "  help        - Show this help message"

# Declare phony targets
.PHONY: all clean install run debug sample_data help
