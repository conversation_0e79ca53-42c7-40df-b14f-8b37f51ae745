#include "course_system.h"

// Local static variables for utility functions
static int input_validation_errors = 0;

void clear_input_buffer(void) {
    int c;
    while ((c = getchar()) != '\n' && c != EOF);
}

int get_integer_input(void) {
    int value;
    int result;
    
    // Input validation using do-while loop
    do {
        result = scanf("%d", &value);
        if (result != 1) {
            printf("Invalid input! Please enter a number: ");
            clear_input_buffer();
            input_validation_errors++; // Static variable increment
        }
    } while (result != 1);
    
    clear_input_buffer();
    return value;
}

float get_float_input(void) {
    float value;
    int result;
    
    // Input validation using do-while loop
    do {
        result = scanf("%f", &value);
        if (result != 1) {
            printf("Invalid input! Please enter a decimal number: ");
            clear_input_buffer();
            input_validation_errors++;
        }
    } while (result != 1);
    
    clear_input_buffer();
    return value;
}

void get_string_input(char *str, int max_length) {
    if (fgets(str, max_length, stdin) != NULL) {
        // Remove newline character if present using pointer arithmetic
        char *newline_pos = strchr(str, '\n');
        if (newline_pos != NULL) {
            *newline_pos = '\0';
        }
    }
}

void swap_courses(Course *a, Course *b) {
    // Swap using temporary variable and structure assignment
    Course temp = *a;
    *a = *b;
    *b = temp;
}

void print_course_details(Course *course) {
    printf("%-10s %-30s %-8d %-20s %-20s\n",
           course->course_code,
           course->course_name,
           course->credits,
           course->prerequisites,
           course->instructor);
}

void print_section_details(Section *section) {
    printf("%-10s %-10s %-20s %-15s %-8d %-8d\n",
           section->section_id,
           section->course_code,
           section->time_slot,
           section->room,
           section->capacity,
           section->enrolled_count);
}

void print_student_details(Student *student) {
    printf("%-6d %-25s %-30s %-8d %-6.2f %-8d\n",
           student->student_id,
           student->name,
           student->email,
           student->semester,
           student->gpa,
           student->registered_courses_count);
}

// File operations using basic I/O functions
void save_data_to_file(void) {
    FILE *file = fopen("course_data.txt", "w");
    if (file == NULL) {
        printf("Error: Could not open file for writing.\n");
        return;
    }
    
    // Save courses using fprintf
    fprintf(file, "COURSES %d\n", total_courses);
    for (int i = 0; i < total_courses; i++) {
        fprintf(file, "%s|%s|%d|%s|%s\n",
                courses[i].course_code,
                courses[i].course_name,
                courses[i].credits,
                courses[i].prerequisites,
                courses[i].instructor);
    }
    
    // Save sections
    fprintf(file, "SECTIONS %d\n", total_sections);
    for (int i = 0; i < total_sections; i++) {
        fprintf(file, "%s|%s|%s|%s|%d|%d",
                sections[i].section_id,
                sections[i].course_code,
                sections[i].time_slot,
                sections[i].room,
                sections[i].capacity,
                sections[i].enrolled_count);
        
        // Save enrolled student IDs using for loop
        for (int j = 0; j < sections[i].enrolled_count; j++) {
            fprintf(file, "|%d", sections[i].student_ids[j]);
        }
        fprintf(file, "\n");
    }
    
    // Save students
    fprintf(file, "STUDENTS %d\n", total_students);
    for (int i = 0; i < total_students; i++) {
        fprintf(file, "%d|%s|%s|%d|%.2f|%d",
                students[i].student_id,
                students[i].name,
                students[i].email,
                students[i].semester,
                students[i].gpa,
                students[i].registered_courses_count);
        
        // Save registered courses using for loop
        for (int j = 0; j < students[i].registered_courses_count; j++) {
            fprintf(file, "|%s|%d", 
                    students[i].registered_courses[j],
                    students[i].registered_sections[j]);
        }
        fprintf(file, "\n");
    }
    
    fclose(file);
}

void load_data_from_file(void) {
    FILE *file = fopen("course_data.txt", "r");
    if (file == NULL) {
        printf("No existing data file found. Starting fresh.\n");
        return;
    }
    
    char line[500];
    char section[20];
    int count;
    
    // Load courses using fscanf and fgets
    if (fgets(line, sizeof(line), file) != NULL) {
        sscanf(line, "%s %d", section, &count);
        if (strcmp(section, "COURSES") == 0) {
            total_courses = count;
            
            // Read course data using for loop
            for (int i = 0; i < total_courses && i < MAX_COURSES; i++) {
                if (fgets(line, sizeof(line), file) != NULL) {
                    // Parse course data using strtok
                    char *token = strtok(line, "|");
                    if (token) strcpy(courses[i].course_code, token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(courses[i].course_name, token);
                    
                    token = strtok(NULL, "|");
                    if (token) courses[i].credits = atoi(token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(courses[i].prerequisites, token);
                    
                    token = strtok(NULL, "|\n");
                    if (token) strcpy(courses[i].instructor, token);
                }
            }
        }
    }
    
    // Load sections
    if (fgets(line, sizeof(line), file) != NULL) {
        sscanf(line, "%s %d", section, &count);
        if (strcmp(section, "SECTIONS") == 0) {
            total_sections = count;
            
            for (int i = 0; i < total_sections && i < MAX_SECTIONS; i++) {
                if (fgets(line, sizeof(line), file) != NULL) {
                    char *token = strtok(line, "|");
                    if (token) strcpy(sections[i].section_id, token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(sections[i].course_code, token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(sections[i].time_slot, token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(sections[i].room, token);
                    
                    token = strtok(NULL, "|");
                    if (token) sections[i].capacity = atoi(token);
                    
                    token = strtok(NULL, "|");
                    if (token) sections[i].enrolled_count = atoi(token);
                    
                    // Load enrolled student IDs using while loop
                    int j = 0;
                    while ((token = strtok(NULL, "|\n")) != NULL && j < sections[i].enrolled_count) {
                        sections[i].student_ids[j] = atoi(token);
                        j++;
                    }
                    
                    // Initialize remaining slots
                    while (j < MAX_ENROLLED) {
                        sections[i].student_ids[j] = -1;
                        j++;
                    }
                }
            }
        }
    }
    
    // Load students
    if (fgets(line, sizeof(line), file) != NULL) {
        sscanf(line, "%s %d", section, &count);
        if (strcmp(section, "STUDENTS") == 0) {
            total_students = count;
            
            for (int i = 0; i < total_students && i < MAX_STUDENTS; i++) {
                if (fgets(line, sizeof(line), file) != NULL) {
                    char *token = strtok(line, "|");
                    if (token) students[i].student_id = atoi(token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(students[i].name, token);
                    
                    token = strtok(NULL, "|");
                    if (token) strcpy(students[i].email, token);
                    
                    token = strtok(NULL, "|");
                    if (token) students[i].semester = atoi(token);
                    
                    token = strtok(NULL, "|");
                    if (token) students[i].gpa = atof(token);
                    
                    token = strtok(NULL, "|");
                    if (token) students[i].registered_courses_count = atoi(token);
                    
                    // Load registered courses using for loop
                    for (int j = 0; j < students[i].registered_courses_count; j++) {
                        token = strtok(NULL, "|");
                        if (token) strcpy(students[i].registered_courses[j], token);
                        
                        token = strtok(NULL, "|");
                        if (token) students[i].registered_sections[j] = atoi(token);
                    }
                }
            }
        }
    }
    
    fclose(file);
    printf("Data loaded successfully!\n");
    printf("Loaded: %d courses, %d sections, %d students\n", 
           total_courses, total_sections, total_students);
}
