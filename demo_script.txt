# Demo Script for Course Registration System
# This file shows how to test the program manually

# 1. Compile the program:
gcc -Wall -Wextra -std=c99 -g main.c course_management.c student_management.c utils.c -o course_system

# 2. Run the program:
./course_system

# 3. Sample test sequence:
# Menu Option 2: Display All Courses (shows loaded courses)
# Menu Option 5: Display All Sections (shows loaded sections)
# Menu Option 6: Add Student
#   - Enter name: <PERSON>
#   - Enter email: <EMAIL>
#   - Enter semester: 2
#   - Enter GPA: 3.5
# Menu Option 7: Display All Students
# Menu Option 8: Register Student for Section
#   - Enter student ID: 1001 (auto-generated)
#   - Enter section ID: A
# Menu Option 3: Sort Courses
#   - Choose option 1 for Bubble Sort by name
# Menu Option 10: Search Course by Code
#   - Enter course code: CS101
# Menu Option 13: Save Data
# Menu Option 0: Exit

# The program demonstrates:
# - All C data types (int, char, float, arrays, structures)
# - All operators (arithmetic, logical, relational)
# - All control structures (if-else, for, while, do-while)
# - All variable types (global, local, static, register)
# - Arrays and sorting algorithms
# - Pointer operations and pointer to pointer
# - File I/O operations
# - Comprehensive structure usage
